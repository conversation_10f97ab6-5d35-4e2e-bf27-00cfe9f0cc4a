{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable",
            "cargo": {
                "args": [
                    "build",
                    "--bin=${input:cargoExecutable}",
                    "--package=${workspaceFolderBasename}"
                ],
                "filter": {
                    "name": "${input:cargoExecutable}",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--bin=${input:cargoExecutable}",
                    "--package=${workspaceFolderBasename}"
                ],
                "filter": {
                    "name": "${input:cargoExecutable}",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        }
    ],
    "inputs": [
        {
            "id": "cargoExecutable",
            "type": "promptString",
            "description": "Name of the binary to debug",
            "default": "main"
        }
    ]
}