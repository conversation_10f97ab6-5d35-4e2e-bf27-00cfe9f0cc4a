[package]
name = "solana-token-seller"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
bs58 = "0.5.1"
clap = { version = "4.4", features = ["derive"] }
dotenv = "0.15.0"
reqwest = { version = "0.12.15", features = ["json"] }
serde_json = "1.0"
solana-client = "2.2.6"
solana-program = "2.2.1"
solana-sdk = "2.2.2"
tokio = { version = "1.37.0", features = ["full"] }
bincode = "1.3.3"
lazy_static = "1.4.0"
base64 = "0.22.1"
deadpool-redis = "0.20.0"
redis = { version = "0.29.2", features = ["tokio-comp"] }
tokio-stream = "0.1"

# Updated Yellowstone versions that are compatible with each other
yellowstone-grpc-proto = "6.0.0"
yellowstone-grpc-client = "6.0.0"

tonic = "0.12.1"
#prost = "0.11"
chrono = { version = "0.4", features = ["serde"] }
futures = "0.3"
log = "0.4.17"
maplit = "1.0.2"
lapin = "2.3.1"
tokio-util = "0.7"
rabbitmq-stream-client = "0.4.0"
serde = { version = "1.0", features = ["derive"] }
async-trait = "0.1"
mysql = "26.0.0"
carbon-pump-swap-decoder = "0.8.1"
borsh = { version =  "1.5.7", features = ["derive"] }
borsh-derive = "1.5.7"
anchor-lang = { version = "0.31.0"}
spl-associated-token-account = "6"
spl-token = "7"
once_cell = "1.19"
