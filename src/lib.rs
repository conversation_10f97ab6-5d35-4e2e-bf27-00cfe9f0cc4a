pub mod instruction;

use anchor_lang::declare_program;
use solana_program::pubkey::Pubkey;


declare_program!(pump_amm_idl);

//const PUBKEY_PUMPFUN: &str = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
//const  PUBKEY_PUMPFUN_ID: Pubkey = Pubkey::from_str_const(PUBKEY_PUMPFUN);
pub(crate) mod wsol {
    solana_program::declare_id!("So11111111111111111111111111111111111111112");
}

pub(crate) fn coin_creator_vault_authority(coin_creator: Pubkey) -> Pubkey {
    let (pump_pool_authority, _) = Pubkey::find_program_address(
        &[b"creator_vault", &coin_creator.to_bytes()],
        &pump_amm_idl::ID,
    );
    pump_pool_authority
}

pub(crate) fn coin_creator_vault_ata(coin_creator: <PERSON><PERSON>,quote_mint: &Pubkey) -> Pubkey {
    let creator_vault_authority = coin_creator_vault_authority(coin_creator);
    let associated_token_creator_vault_authority =
        spl_associated_token_account::get_associated_token_address_with_program_id(
            &creator_vault_authority,
            quote_mint,
            &spl_token::id(),
        );
    associated_token_creator_vault_authority
}