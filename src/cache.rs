use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use crate::db::AppConfig;

pub struct TokenConfig {
    pub enabled: bool,
    pub is_simulation: bool,
    pub buy_sol: f64,
    pub max_time_sec: u64,
    pub min_time_sec: u64,
    pub max_profit_sol: f64,
    pub min_profit_sol: f64,
    pub max_user_buy_sol: f64,
    pub max_mc_pct: u64,
    pub min_mc_pct: u64,
    pub address: String,
    pub platform: String,
}

impl Default for TokenConfig {
    fn default() -> Self {
        TokenConfig {
            enabled: false,
            is_simulation: true,
            buy_sol: 0.05,
            max_time_sec: 1200,
            min_time_sec: 0,
            max_profit_sol: 1.8,
            min_profit_sol: 0.5,
            max_user_buy_sol: 0.89,
            max_mc_pct: 60,
            min_mc_pct: 30,
            address: String::from("C68ihvpfBjxts7iEkstnLrE4eLVo2xwigfEKRc2UcAAF"),
            platform: String::from("PUMP"),
        }
    }
}

pub type SharedCache = Arc<Mutex<HashMap<String, HashMap<String, String>>>>;

pub fn new_cache() -> SharedCache {
    Arc::new(Mutex::new(HashMap::new()))
}

pub fn set_config(cache: &SharedCache, token: &str, config: TokenConfig) {
    let mut locked = cache.lock().unwrap();
    let mut config_map = HashMap::new();
    config_map.insert("enabled".to_string(), config.enabled.to_string());
    config_map.insert("is_simulation".to_string(), config.is_simulation.to_string());
    config_map.insert("buy_sol".to_string(), config.buy_sol.to_string());
    config_map.insert("max_time_sec".to_string(), config.max_time_sec.to_string());
    config_map.insert("min_time_sec".to_string(), config.min_time_sec.to_string());
    config_map.insert("max_profit_sol".to_string(), config.max_profit_sol.to_string());
    config_map.insert("min_profit_sol".to_string(), config.min_profit_sol.to_string());
    config_map.insert("max_user_buy_sol".to_string(), config.max_user_buy_sol.to_string());
    config_map.insert("max_mc_pct".to_string(), config.max_mc_pct.to_string());
    config_map.insert("min_mc_pct".to_string(), config.min_mc_pct.to_string());
    config_map.insert("address".to_string(), config.address);
    config_map.insert("platform".to_string(), config.platform);
    locked.insert(token.to_string(), config_map);
}

pub fn get_config(cache: &SharedCache, token: &str) -> Option<TokenConfig> {
    let locked = cache.lock().unwrap();
    locked.get(token).cloned().map(|config_map| TokenConfig {
        enabled: config_map.get("enabled").unwrap_or(&"false".to_string()) == "true",
        is_simulation: config_map.get("is_simulation").unwrap_or(&"true".to_string()) == "true",
        buy_sol: config_map.get("buy_sol").unwrap_or(&"0.0".to_string()).parse().unwrap_or(0.0),
        max_time_sec: config_map.get("max_time_sec").unwrap_or(&"0".to_string()).parse().unwrap_or(0),
        min_time_sec: config_map.get("min_time_sec").unwrap_or(&"0".to_string()).parse().unwrap_or(0),
        max_profit_sol: config_map.get("max_profit_sol").unwrap_or(&"0.0".to_string()).parse().unwrap_or(0.0),
        min_profit_sol: config_map.get("min_profit_sol").unwrap_or(&"0.0".to_string()).parse().unwrap_or(0.0),
        max_user_buy_sol: config_map.get("max_user_buy_sol").unwrap_or(&"0.0".to_string()).parse().unwrap_or(0.0),
        max_mc_pct: config_map.get("max_mc_pct").unwrap_or(&"0".to_string()).parse().unwrap_or(0),
        min_mc_pct: config_map.get("min_mc_pct").unwrap_or(&"0".to_string()).parse().unwrap_or(0),
        address: config_map.get("address").unwrap_or(&"".to_string()).clone(),
        platform: config_map.get("platform").unwrap_or(&"".to_string()).clone(),
    })
}

pub type Cache = Mutex<HashMap<String, HashMap<String, String>>>;

pub fn get_app_config(cache: &Cache, app_name: &str) -> Option<AppConfig> {
    let cache_lock = cache.lock().unwrap();
    let app_config_map = cache_lock.get(app_name)?;

    Some(AppConfig {
        enabled: app_config_map.get("enabled")?.parse().ok()?,
        is_simulation: app_config_map.get("is_simulation")?.parse().ok()?,
        buy_sol: app_config_map.get("buy_sol")?.parse().ok()?,
        max_time_sec: app_config_map.get("max_time_sec")?.parse().ok()?,
        min_time_sec: app_config_map.get("min_time_sec")?.parse().ok()?,
        max_profit_sol: app_config_map.get("max_profit_sol")?.parse().ok()?,
        min_profit_sol: app_config_map.get("min_profit_sol")?.parse().ok()?,
        max_user_buy_sol: app_config_map.get("max_user_buy_sol")?.parse().ok()?,
        max_mc_pct: app_config_map.get("max_mc_pct")?.parse().ok()?,
        min_mc_pct: app_config_map.get("min_mc_pct")?.parse().ok()?,
        address: app_config_map.get("address")?.clone(),
        platform: app_config_map.get("platform")?.clone(),
    })
}


