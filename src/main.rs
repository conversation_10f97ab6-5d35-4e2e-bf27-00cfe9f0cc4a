mod cache;
mod client;
mod common; // Make sure this is included
mod db;
mod grpc;
mod token;
mod trade;
mod trends;
mod event;
mod event_bus;

use crate::event::SolanaEvent;
use crate::event_bus::{init_event_bus, emit_event};

use anchor_lang::accounts::option;
use anchor_lang::declare_program;
use anyhow::{Context, Result};
pub use borsh::{BorshDeserialize, BorshSerialize};
use cache::{ new_cache};
use clap::Parser;
use client::create_http_client;
use client::get_http_client;
use client::warmup_jito_connection;
use deadpool_redis::redis::AsyncCommands;
use deadpool_redis::{Config, Runtime};
use lazy_static::lazy_static;
use reqwest::Client as HttpClient;
use solana_client::rpc_client::RpcClient;
use solana_program::pubkey::Pubkey;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::signer::Signer;
use solana_token_seller::instruction;
use std::env;
use std::sync::Arc;
use std::{collections::HashMap, str::FromStr, time::Instant};
use token::check_transaction_status;
use token::get_token_balance;
use trade::{send_tx_jito,sell_tokens_jito}; // Import AsyncCommands trait
use spl_associated_token_account::get_associated_token_address;
use std::sync::Mutex;
use instruction::{
    BuyOption, PoolInfo, AdditionalAccounts,PoolMintInfo
};


//use wsol;
// Global HTTP client for connection pooling across multiple requests
lazy_static! {
    pub(crate) static ref HTTP_CLIENT: Arc<Result<HttpClient>> = Arc::new(create_http_client());
}

// Global static variables for tracking bought/sold amounts
lazy_static! {
    pub static ref BOUGHT: Arc<Mutex<bool>> = Arc::new(Mutex::new(false));
}

pub fn set_bought(value: bool) {
    if let Ok(mut bought) = BOUGHT.lock() {
        *bought = value;
    }
}

pub fn get_bought() -> bool {
    BOUGHT.lock().map(|bought| *bought).unwrap_or(false)
}

declare_program!(pump_amm_idl);

// CLI Args
#[derive(Parser, Debug)]
#[command(name = "solana-token-seller")]
#[command(about = "CLI tool to sell Solana tokens")]
struct Cli {
    #[arg(short, long, help = "Token mint address")]
    mint: String,

    #[arg(short, long, help = "Amount of tokens to sell")]
    amount: Option<f64>,

    #[arg(short, long, help = "Check token balance")]
    balance: bool,

    #[arg( long, help = "scan for tx")]
    scan: bool,
    #[arg(short, long, help = "scan for tx")]
    redis: bool,
    #[arg(short, long, help = "scan for tx")]
    mysql: bool,
    #[arg(short, long, help = "buy AMM")]
    buy: bool,
    #[arg(short, long, help = "Sol Amount")]
    sol_amount: Option<u64>,
    #[arg(short,long, help = "sell AMM")]
    sell: bool,
    #[arg(short, long, help = "Sol Amount")]
    token_amount: Option<u64>,
    #[arg(short, long, help = "creator address")]
    creator: Option<String>,
    #[arg(short, long, help = "Pool address")]
    pool: Option<String>,
}

#[tokio::main]
async fn main() -> Result<()> {
    init_event_bus().await;

    let cache = new_cache();

    let cache_clone: Arc<std::sync::Mutex<HashMap<String, HashMap<String, String>>>> =
        cache.clone();
    tokio::spawn(async move {
        loop {
            match db::get_config() {
                Ok(configs) => {
                    let mut cache_lock = cache_clone.lock().unwrap();
                    for (app, config) in configs {
                        let config_map = HashMap::from([
                            ("enabled".to_string(), config.enabled.to_string()),
                            (
                                "is_simulation".to_string(),
                                config.is_simulation.to_string(),
                            ),
                            ("buy_sol".to_string(), config.buy_sol.to_string()),
                            ("max_time_sec".to_string(), config.max_time_sec.to_string()),
                            ("min_time_sec".to_string(), config.min_time_sec.to_string()),
                            (
                                "max_profit_sol".to_string(),
                                config.max_profit_sol.to_string(),
                            ),
                            (
                                "min_profit_sol".to_string(),
                                config.min_profit_sol.to_string(),
                            ),
                            (
                                "max_user_buy_sol".to_string(),
                                config.max_user_buy_sol.to_string(),
                            ),
                            ("max_mc_pct".to_string(), config.max_mc_pct.to_string()),
                            ("min_mc_pct".to_string(), config.min_mc_pct.to_string()),
                            ("address".to_string(), config.address.clone()),
                            ("platform".to_string(), config.platform.clone()),
                        ]);
                        cache_lock.insert(app, config_map);
                    }
                    //println!("Cache updated successfully");
                }
                Err(e) => {
                    eprintln!("Failed to update cache from database: {}", e);
                }
            }
            tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
        }
    });

    if let Err(err) = dotenv::dotenv() {
        eprintln!("Warning: Could not load .env file: {}", err);
        println!("Continuing without .env file, ensure environment variables are set manually");
    } else {
        //println!("Successfully loaded .env file");
    }

    if let Err(e) = get_http_client() {
        eprintln!("Error initializing HTTP client: {}", e);
        eprintln!("Will try to create a new client for each request");
    } else {
        //println!("HTTP client initialized successfully");
    }

    // Warmup the connection to Jito endpoint
    if let Err(e) = warmup_jito_connection().await {
        println!("Failed to warm up Jito connection: {}", e);
        println!("Continuing anyway...");
    }

    // Verify PRIVATE_KEY is available
    if env::var("PRIVATE_KEY").is_err() {
        eprintln!("PRIVATE_KEY environment variable is not set!");
        println!("Please ensure PRIVATE_KEY is set in .env file or environment");
    } else {
        //println!("PRIVATE_KEY environment variable is set");
    }

    lazy_static! {
        static ref REDIS_POOL: Arc<Result<deadpool_redis::Pool, anyhow::Error>> = Arc::new(
            //Config::from_url("unix:/run/redis/redis-server-unix.sock")
            Config::from_url("redis://:pump2pump@149.56.241.194:6379")
                .create_pool(Some(Runtime::Tokio1))
                .map_err(|e| anyhow::anyhow!(e))
        );
    }

    // Initialize database connection pool
    if let Err(e) = db::init_database() {
        eprintln!("Failed to initialize database connection pool: {}", e);
        println!("Database features may not work properly");
    }
    let pool = REDIS_POOL
        .as_ref()
        .as_ref()
        .map_err(|e| anyhow::anyhow!(e))?;
    let mut conn = pool.get().await?;

    // Parse command line arguments
    let cli = Cli::parse();

    // Setup RPC connection
    let rpc_url = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek";
    let client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::processed());

    let mint_pubkey = Pubkey::from_str(&cli.mint).context("Invalid mint address")?;

    if cli.balance {
        // Check token balance
        let balance = get_token_balance(&client, &mint_pubkey)?;
        println!("Balance for mint {}: {}", cli.mint, balance);
    } else if let Some(amount) = cli.amount {
        println!("Selling now...");
        let start_time = Instant::now();

        match sell_tokens_jito(&client, &mint_pubkey, amount).await {
            Ok(tx_id) => {
                let submit_time = Instant::now();
                let submit_duration = submit_time.duration_since(start_time).as_millis();
                println!("Sold: {} - {}", cli.mint, tx_id);
                println!("Time to submit transaction: {}ms", submit_duration);

                // Check transaction status
                let success = check_transaction_status(&client, &tx_id, start_time).await?;
                std::process::exit(if success { 0 } else { 1 });
            }
            Err(err) => {
                let error_time = Instant::now();
                let error_duration = error_time.duration_since(start_time).as_millis();
                eprintln!("Failed to sell tokens for mint: {} - {}", cli.mint, err);
                eprintln!("Time until error: {}ms", error_duration);
                std::process::exit(1);
            }
        }
    } else if cli.scan {
        let grpc_cache = cache.clone(); // Clone the cache to pass to the gRPC handle
        let grpc_pool = pool.clone(); // Pass the Redis connection pool instead of a single connection

        let grpc_handle = tokio::spawn(async move {
        
            match grpc::grpc(grpc_cache, grpc_pool).await {
                Ok(result) => Ok(result),
                Err(e) => {
                    eprintln!("Error in GRPC processing: {}", e);
                    Err(anyhow::anyhow!("GRPC processing error: {}", e))
                }
            }
        });
        
        // Handle the result from the spawned task
        match grpc_handle.await {
            Ok(result) => result?,
            Err(e) => return Err(anyhow::anyhow!("Task join error: {}", e)),
        }
    } else if cli.mysql {
        // MySQL testing
        println!("Testing MySQL connection and user queries");

        // Test the database connection
        match db::test_database_connection() {
            Ok(_) => println!("Database connection established successfully"),
            Err(e) => {
                eprintln!("Database connection error: {}", e);
                std::process::exit(1);
            }
        }

        // Test get_all_transactions function
        let starttime = Instant::now();
        match db::get_all_transactions() {
            Ok(transactions) => {
                println!(
                    "Retrieved {} transactions from the database:",
                    transactions.len()
                );
                let elapsed = starttime.elapsed().as_millis();
                println!("get_all_transactions took: {}ms", elapsed);

                // for transaction in transactions {
                //     println!(
                //         "ID: {}, Mint: {}, Account: {}, Signer: {}, SOL Amount: {:?}, Token Amount: {:?}, Trade: {:?}, Price: {:?}, Market Cup: {:?}, Block Time: {}, Block ID: {}, Signature: {}, App: {:?}",
                //         transaction.id,
                //         transaction.mint,
                //         transaction.account,
                //         transaction.signer,
                //         transaction.sol_amount,
                //         transaction.token_amount,
                //         transaction.trade,
                //         transaction.price,
                //         transaction.market_cup,
                //         transaction.block_time,
                //         transaction.block_id,
                //         transaction.signature,
                //         transaction.app
                //     );
                // }
            }
            Err(e) => eprintln!("Error retrieving transactions: {}", e),
        }
    } else if cli.redis {
        // Scan for transactions
        // Test Redis read and write with deadpool-redis

        let write_start = Instant::now();
        let _: () = conn.set("testkeywrite", "value").await?; // Use AsyncCommands trait
        let write_duration = write_start.elapsed().as_micros();
        println!("Write operation took: {}µs", write_duration);

        // Read operation
        let read_start = Instant::now();
        let val: String = conn.get("testkeywrite").await?; // Use AsyncCommands trait
        let read_duration = read_start.elapsed().as_micros();
        println!("Read operation took: {}µs", read_duration);
        println!("Got from Redis: {}", val);

        // Read operation
        let read_start2 = Instant::now();
        let val2: String = conn.get("testkeywrite").await?; // Use AsyncCommands trait
        let read_duration2 = read_start2.elapsed().as_micros();
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
        println!("Read operation took: {}µs", read_duration2);
        println!("Got from Redis: {}", val2);

        let print_start = Instant::now();
        println!("   HASHMAP : {:?}", cache.lock().unwrap().get("auto1"));
        println!("   HASHMAP : {:?}", cache.lock().unwrap().get("auto5"));
        if let Some(config) = cache::get_app_config(&cache, "auto1") {
            println!("HashMap buy_sol: {:?}", config.buy_sol);
        } else {
            println!("No configuration found for 'auto1'");
        }
        let print_duration = print_start.elapsed().as_micros();
        println!("Print operation took: {}µs", print_duration);
    } else if cli.buy {
        // Handle buy AMM logic
        if let Some(sol_amount) = cli.sol_amount {
            // ensure creator and pool are set
            if cli.creator.is_none() || cli.pool.is_none() || cli.token_amount.is_none() ||
               cli.creator.as_ref().unwrap().is_empty() || cli.pool.as_ref().unwrap().is_empty() {
                eprintln!("Please provide valid creator and pool addresses");
                std::process::exit(1);
            }
            let token_amount = cli.token_amount.unwrap_or(0);
            println!("Buying AMM with {} SOL", sol_amount);
            env::var("PRIVATE_KEY").context("PRIVATE_KEY environment variable is not set")?;
            // convert private key to keypair
            let private_key =
                env::var("PRIVATE_KEY").context("Missing PRIVATE_KEY environment variable")?;
            let owner = token::get_keypair_from_base58(&private_key)
                .context("Failed to convert PRIVATE_KEY to keypair")?;

            let owner_pubkey = owner.pubkey();
            let  mint_address: &str = &cli.mint; // WSOL mint address
            const WSOL_ADDRESS: &str = "So11111111111111111111111111111111111111112"; // WSOL mint address
            let creator_str = cli.creator.as_ref().unwrap(); // Creator address
            let pool_address: &str = cli.pool.as_ref().unwrap(); // AMM pool address
            let mint_pubkey: Pubkey = Pubkey::from_str(mint_address).context("Invalid mint address for MINT")?;
            let wsol_pubkey: Pubkey = Pubkey::from_str(WSOL_ADDRESS).context("Invalid mint address for WSOL")?;
            let creator_pubkey: Pubkey = Pubkey::from_str(creator_str).context("Invalid creator address")?;
            let pool_pubkey : Pubkey = Pubkey::from_str(pool_address).context("Invalid pool address")?; 

            
            let pool_base_token_account = get_associated_token_address(&pool_pubkey, &mint_pubkey);
            let pool_quote_token_account = get_associated_token_address(&pool_pubkey, &wsol_pubkey);

            let pool_info = PoolInfo {
                pool: pool_pubkey,
                pool_base_token_account,
                pool_quote_token_account,
            };


            let option = BuyOption {
                base_amount_out: token_amount,
                max_quote_amount_in: sol_amount, // Set to 0 for simplicity, adjust as needed
            };
            let additional_accounts = AdditionalAccounts {
                coin_creator: creator_pubkey,
            };
            
            let pool_mint_info = PoolMintInfo {
                pool: pool_pubkey,
                base_mint: mint_pubkey,
                quote_mint: wsol_pubkey,
            };
            let  inst: Vec<solana_sdk::instruction::Instruction> = instruction::buy_instruction(&pool_info,&pool_mint_info, &option, &mint_pubkey, &owner_pubkey, additional_accounts);

            println!("Buy instruction created successfully");
            // Here you would typically send the instruction to the Solana network
            // For example, using the client to send the transaction:
            send_tx_jito(&client, &inst).await?;


            //
            //instruction::buy_amm_instruction(
            //     &client,
            //     &mint_pubkey,
            //     sol_amount,
            //     &cache,
            // ).await?;
       

        } else {
            eprintln!("Please provide a valid SOL amount to buy AMM");
            std::process::exit(1);
        }
    } else if cli.sell {
        // Handle buy AMM logic
        if let Some(token_amount) = cli.token_amount {
            println!("Selling AMM with {} tokens", token_amount);
            if cli.creator.is_none() || cli.pool.is_none() || 
               cli.creator.as_ref().unwrap().is_empty() || cli.pool.as_ref().unwrap().is_empty() {
                eprintln!("Please provide valid creator and pool addresses");
                std::process::exit(1);
            }
            env::var("PRIVATE_KEY").context("PRIVATE_KEY environment variable is not set")?;
            // convert private key to keypair
            let private_key =
                env::var("PRIVATE_KEY").context("Missing PRIVATE_KEY environment variable")?;
            let owner = token::get_keypair_from_base58(&private_key)
                .context("Failed to convert PRIVATE_KEY to keypair")?;

                let owner_pubkey = owner.pubkey();
                let  mint_address: &str = &cli.mint; // WSOL mint address
                const WSOL_ADDRESS: &str = "So11111111111111111111111111111111111111112"; // WSOL mint address
                let creator_str = cli.creator.as_ref().unwrap(); // Creator address
                let pool_address: &str = cli.pool.as_ref().unwrap(); // AMM pool address
                let mint_pubkey: Pubkey = Pubkey::from_str(mint_address).context("Invalid mint address for MINT")?;
                let wsol_pubkey: Pubkey = Pubkey::from_str(WSOL_ADDRESS).context("Invalid mint address for WSOL")?;
                let creator_pubkey: Pubkey = Pubkey::from_str(creator_str).context("Invalid creator address")?;
                let pool_pubkey : Pubkey = Pubkey::from_str(pool_address).context("Invalid pool address")?; 
    

            let pool_base_token_account = get_associated_token_address(&pool_pubkey, &mint_pubkey);
            let pool_quote_token_account = get_associated_token_address(&pool_pubkey, &wsol_pubkey);
            let pool_info = PoolInfo {
                pool: pool_pubkey,
                pool_base_token_account,
                pool_quote_token_account,
            };
            let option = instruction::SellOption {
                base_amount_in: token_amount,
                min_quote_amount_out: 0, // Set to 0 for simplicity, adjust as needed
            };
            let additional_accounts = instruction::AdditionalAccounts {
                coin_creator: creator_pubkey,
            };
            let pool_mint_info = PoolMintInfo {
                pool: pool_pubkey,
                base_mint: mint_pubkey,
                quote_mint: wsol_pubkey,
            };
            //let  inst: Vec<solana_sdk::instruction::Instruction> = instruction::buy_instruction(&pool_info, &option, &mint_pubkey, &owner_pubkey, additional_accounts);
            let inst: solana_sdk::instruction::Instruction = instruction::sell_instruction(
                &pool_info,
                &pool_mint_info,
                &option,
                &mint_pubkey,
                &owner_pubkey,
                additional_accounts,
            );
            //println!("Sell instruction created successfully");
            let insts = vec![inst];

            send_tx_jito(&client, &insts).await?;

        } else {
            eprintln!("Please provide a valid token amount to sell AMM");
            std::process::exit(1);
        }
    } else {
        eprintln!("No valid command provided");
        std::process::exit(1);
    }

    Ok(())
}
