use crate::pump_amm_idl::client::accounts::Buy;
use crate::{coin_creator_vault_ata, coin_creator_vault_authority, pump_amm_idl, wsol};
use anchor_lang::prelude::Pubkey;
use anchor_lang::{InstructionData, ToAccountMetas};
use solana_program::instruction::Instruction;
use spl_associated_token_account::get_associated_token_address;
use std::str::FromStr;




const GLOBAL_CONFIG: Pubkey =
    Pubkey::from_str_const("ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw");

const EVENT_AUTHORITY: Pubkey =
    Pubkey::from_str_const("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR");

const PROTOCOL_FEE_RECIPIENT: Pubkey =
    Pubkey::from_str_const("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV");
const PROTOCOL_FEE_RECIPIENT5: Pubkey =
    Pubkey::from_str_const("7VtfL8fvgNfhz17qKRMjzQEXgbdpnHHHQRh54R9jP2RJ");

const PROTOCOL_FEE_RECIPIENT_TOKEN_ACCOUNT: Pubkey =
    Pubkey::from_str_const("94qWNrtmfn42h3ZjUZwWvK1MEo9uVmmrBPd2hpNjYDjb");

/**
     *   Timestamp: **********
  Index: 0
 x  Creator: H1TBNKNBAHEmBKQKn3k3BJEw51xJDuE1KPuEgvkeXyCb
  Base Mint: So11111111111111111111111111111111111111112
x   Quote Mint: E5QsKFSGsrZo2BvDRCeK1SAenJsj4JCU9ucCXjFyK1KD
  Base Mint Decimals: 9
  Quote Mint Decimals: 6
  Base Amount In: ***********
  Quote Amount In: **************
  Pool Base Amount: ***********
  Pool Quote Amount: **************
  Minimum Liquidity: 100
  Initial Liquidity: *************
  LP Token Amount Out: *************
  Pool Bump: 255
x  Pool: Bncr7Cm7rH97cDRvfo9NJxW7DFaq8mbagRULLiVvSmD4
  LP Mint: 5yPeNcZXP3BSm9LBzaYYtw8uA2igz3RiH83TyXwzmMdN
  User Base Token Account: 3cd7rreFwCANkmCGTxoHHQC8o6yurfzHLkwV4w3HciW2
  User Quote Token Account: FBc6JcYUydECwau7BvxPoKmLKVKfyfj7WYKgLkVQyY73
  Coin creator: 11111111111111111111111111111111
  Terminal URL: https://neo.bullx.io/terminal?chainId=**********&address=E5QsKFSGsrZo2BvDRCeK1SAenJsj4JCU9ucCXjFyK1KD

     */

#[derive(Debug, Copy, Clone)]
pub struct PoolInfo {
    pub pool: Pubkey,
    pub pool_base_token_account: Pubkey,
    pub pool_quote_token_account: Pubkey,
}

pub struct PoolMintInfo {
    pub pool: Pubkey,
    pub base_mint: Pubkey,
    pub quote_mint: Pubkey,
}

#[derive(Debug, Clone)]
pub struct BuyOption {
    pub base_amount_out: u64,     // 兑换token amount
    pub max_quote_amount_in: u64, // 最多需要多少wsol
}
#[derive(Debug, Clone)]
pub struct SellOption {
    pub base_amount_in: u64,
    pub min_quote_amount_out: u64,
}

pub struct SwapOption {
    pub amount_in: u64,
    pub amount_out: u64,
}

#[derive(Debug, Clone, Copy)]
pub struct AdditionalAccounts {
    pub coin_creator: Pubkey,
}

impl AdditionalAccounts {
    pub fn coin_creator_vault_authority_ata(&self, quote_mint: &Pubkey) -> (Pubkey, Pubkey) {
        let creator_vault_ata = coin_creator_vault_ata(self.coin_creator, &quote_mint);

        let creator_vault_authority = coin_creator_vault_authority(self.coin_creator);

        (creator_vault_ata, creator_vault_authority)
    }
}

fn create_ata_token_account_instr(
    token_program: Pubkey,
    mint: &Pubkey,
    owner: &Pubkey,
) -> Instruction {
    let associated_token_account_idempotent =
        spl_associated_token_account::instruction::create_associated_token_account_idempotent(
            owner,
            owner,
            mint,
            &token_program,
        );
    associated_token_account_idempotent
}

pub fn sell_instruction(
    pool: &PoolInfo,
    pool_mint_info: &PoolMintInfo,

    sell_option: &SellOption,
    mint: &Pubkey,
    signer: &Pubkey,
    additional_accounts: AdditionalAccounts,
) -> Instruction {
    let associated_token_address = get_associated_token_address(signer, mint);

    let associated_quote_address = get_associated_token_address(signer, &wsol::id());

    let (coin_creator_vault_ata, coin_creator_vault_authority) =
        additional_accounts.coin_creator_vault_authority_ata(&pool_mint_info.quote_mint);

    let sell_account_meta = pump_amm_idl::client::accounts::Sell {
        pool: pool.pool,
        user: *signer,
        global_config: GLOBAL_CONFIG,
        base_mint: pool_mint_info.base_mint,
        quote_mint: pool_mint_info.quote_mint,
        user_base_token_account: associated_token_address,
        user_quote_token_account: associated_quote_address,
        pool_base_token_account: pool.pool_base_token_account,
        pool_quote_token_account: pool.pool_quote_token_account,
        protocol_fee_recipient: PROTOCOL_FEE_RECIPIENT,
        protocol_fee_recipient_token_account: PROTOCOL_FEE_RECIPIENT_TOKEN_ACCOUNT,
        base_token_program: spl_token::id(),
        quote_token_program: spl_token::id(),
        system_program: solana_program::system_program::id(),
        associated_token_program: spl_associated_token_account::id(),
        event_authority: EVENT_AUTHORITY,
        program: pump_amm_idl::ID,
        coin_creator_vault_ata,
        coin_creator_vault_authority,
    }
    .to_account_metas(None);

    let sell_data = pump_amm_idl::client::args::Sell {
        base_amount_in: sell_option.base_amount_in,
        min_quote_amount_out: sell_option.min_quote_amount_out,
    }
    .data();

    let pump_swap_sell_instruction =
        Instruction::new_with_bytes(pump_amm_idl::ID, &sell_data, sell_account_meta);
    pump_swap_sell_instruction
}

pub fn swap_instruction(
    pool: &PoolInfo,
    pool_mint_info: &PoolMintInfo,
    option: &SwapOption,
    mint: &Pubkey,
    signer: &Pubkey,
    additional_accounts: AdditionalAccounts,
    protocol_fee_account: Pubkey,
    is_buy: bool,
) -> Vec<Instruction> {
    // println!("all mints:{:?}  check mint:{} MINT_DATA_CACHE: {:?}",list_all_mints(), &mint.to_string(),MINT_DATA_CACHE.lock().unwrap());
    // let mint_data = get_mint_data(&mint.to_string()).unwrap();
    // let protocol_fee_account = mint_data.fee_account;
    println!("protocol_fee_account: {:?}", protocol_fee_account);
    let mut instructions = Vec::new();
    let associated_token_address = get_associated_token_address(signer, mint);
    let associated_wsol_address = get_associated_token_address(signer, &wsol::id());
    let associated_protocol_fee_token_account;

    if pool_mint_info.base_mint.to_string() == "So11111111111111111111111111111111111111112" {
        associated_protocol_fee_token_account =
            get_associated_token_address(&protocol_fee_account, mint);
    } else {
        associated_protocol_fee_token_account =
            get_associated_token_address(&protocol_fee_account, &wsol::id());
    }

    let user_assoc_base_token_account: Pubkey;
    let user_assoc_quote_token_account: Pubkey;

    if pool_mint_info.base_mint.to_string() == "So11111111111111111111111111111111111111112" {
        user_assoc_base_token_account = associated_wsol_address;
        user_assoc_quote_token_account = associated_token_address;
    } else {
        user_assoc_base_token_account = associated_token_address;
        user_assoc_quote_token_account = associated_wsol_address;
    };

    if is_buy {
        // create mint ass token user
        let create_user_base_token_instr =
            create_ata_token_account_instr(spl_token::id(), &mint, &signer);

        // Assume wsol account already exists

        let create_user_quote_token_instr =
            create_ata_token_account_instr(spl_token::id(), &wsol::id(), &signer);

        // Transfer SOL to wsol account and sync
        let transfer_sol_instruction = solana_program::system_instruction::transfer(
            signer,
            &associated_wsol_address,
            option.amount_in,
        );

        let sync_native_instruction =
            spl_token::instruction::sync_native(&spl_token::id(), &associated_wsol_address)
                .unwrap();
        instructions.push(create_user_base_token_instr);
        instructions.push(create_user_quote_token_instr);
        instructions.push(transfer_sol_instruction);
        instructions.push(sync_native_instruction);
    }
    let (coin_creator_vault_ata, coin_creator_vault_authority) =
        additional_accounts.coin_creator_vault_authority_ata(&pool_mint_info.quote_mint);

    let swap_account_meta = pump_amm_idl::client::accounts::Buy::from(Buy {
        pool: pool.pool,
        user: *signer,
        global_config: GLOBAL_CONFIG,
        base_mint: pool_mint_info.base_mint,
        quote_mint: pool_mint_info.quote_mint,
        user_base_token_account: user_assoc_base_token_account,
        user_quote_token_account: user_assoc_quote_token_account,
        pool_base_token_account: pool.pool_base_token_account,
        pool_quote_token_account: pool.pool_quote_token_account,
        protocol_fee_recipient: protocol_fee_account.clone(),
        protocol_fee_recipient_token_account: associated_protocol_fee_token_account,
        base_token_program: spl_token::id(),
        quote_token_program: spl_token::id(),
        system_program: solana_program::system_program::id(),
        associated_token_program: spl_associated_token_account::id(),
        event_authority: EVENT_AUTHORITY,
        program: pump_amm_idl::ID,
        coin_creator_vault_ata,
        coin_creator_vault_authority,
    })
    .to_account_metas(None);
    let swap_instr: Vec<u8>;
    if is_buy {
        if pool_mint_info.base_mint.to_string() == "So11111111111111111111111111111111111111112" {
            swap_instr = pump_amm_idl::client::args::Sell {
                base_amount_in: option.amount_in,
                min_quote_amount_out: option.amount_out,
            }
            .data();
        } else {
            swap_instr = pump_amm_idl::client::args::Buy {
                max_quote_amount_in: option.amount_in,
                base_amount_out: option.amount_out,
            }
            .data();
        }
    } else {
        if pool_mint_info.base_mint.to_string() == "So11111111111111111111111111111111111111112" {
            swap_instr = pump_amm_idl::client::args::Buy {
                max_quote_amount_in: option.amount_in,
                base_amount_out: option.amount_out,
            }
            .data();
        } else {
            swap_instr = pump_amm_idl::client::args::Sell {
                base_amount_in: option.amount_in,
                min_quote_amount_out: option.amount_out,
            }
            .data();
        }
    }

    let pump_swap_instruction =
        Instruction::new_with_bytes(pump_amm_idl::ID, &swap_instr, swap_account_meta);

    instructions.push(pump_swap_instruction);
    if !is_buy {
        let close_wsol_account_instr = spl_token::instruction::close_account(
            &spl_token::id(),
            &associated_wsol_address,
            signer,
            signer,
            &[],
        )
        .unwrap();
        instructions.push(close_wsol_account_instr);
            // close mint associated token account and burn even if there is leftover
    // let burn_wsol_instr = spl_token::instruction::burn(
    //     &spl_token::id(),
    //     &associated_token_address,
    //     mint,
    //     signer,
    //     &[],
    //     option.amount_in,
    // )
    // .unwrap();
    // instructions.push(burn_wsol_instr);

    // let close_token_account_instr = spl_token::instruction::close_account(
    //     &spl_token::id(),
    //     &associated_token_address,
    //     signer,
    //     signer,
    //     &[],
    // )
    // .unwrap();
    // instructions.push(close_token_account_instr);

    }

    

    return instructions;
}

pub fn buy_instruction(
    pool: &PoolInfo,
    pool_mint_info: &PoolMintInfo,
    option: &BuyOption,
    mint: &Pubkey,
    signer: &Pubkey,
    additional_accounts: AdditionalAccounts,
) -> Vec<Instruction> {
    let associated_token_address = get_associated_token_address(signer, mint);

    // create mint ass token user
    let create_user_base_token_instr =
        create_ata_token_account_instr(spl_token::id(), &mint, &signer);

    let associated_quote_address = get_associated_token_address(signer, &wsol::id());

    // Assume wsol account already exists

    let create_user_quote_token_instr =
        create_ata_token_account_instr(spl_token::id(), &wsol::id(), &signer);

    // Transfer SOL to wsol account and sync
    let transfer_sol_instruction = solana_program::system_instruction::transfer(
        signer,
        &associated_quote_address,
        option.max_quote_amount_in,
    );

    let sync_native_instruction =
        spl_token::instruction::sync_native(&spl_token::id(), &associated_quote_address).unwrap();

    let (coin_creator_vault_ata, coin_creator_vault_authority) =
        additional_accounts.coin_creator_vault_authority_ata(&pool_mint_info.base_mint);

    let buy_account_meta = pump_amm_idl::client::accounts::Buy::from(Buy {
        pool: pool.pool,
        user: *signer,
        global_config: GLOBAL_CONFIG,
        base_mint: pool_mint_info.base_mint,
        quote_mint: pool_mint_info.quote_mint,
        user_base_token_account: associated_token_address,
        user_quote_token_account: associated_quote_address,
        pool_base_token_account: pool.pool_base_token_account,
        pool_quote_token_account: pool.pool_quote_token_account,
        protocol_fee_recipient: PROTOCOL_FEE_RECIPIENT,
        protocol_fee_recipient_token_account: PROTOCOL_FEE_RECIPIENT_TOKEN_ACCOUNT,
        base_token_program: spl_token::id(),
        quote_token_program: spl_token::id(),
        system_program: solana_program::system_program::id(),
        associated_token_program: spl_associated_token_account::id(),
        event_authority: EVENT_AUTHORITY,
        program: pump_amm_idl::ID,
        coin_creator_vault_ata,
        coin_creator_vault_authority,
    })
    .to_account_metas(None);

    let buy = pump_amm_idl::client::args::Buy {
        base_amount_out: option.base_amount_out,
        max_quote_amount_in: option.max_quote_amount_in,
    }
    .data();

    let pump_swap_buy_instruction =
        Instruction::new_with_bytes(pump_amm_idl::ID, &buy, buy_account_meta);

    vec![
        create_user_base_token_instr,
        create_user_quote_token_instr,
        transfer_sol_instruction,
        sync_native_instruction,
        pump_swap_buy_instruction,
    ]
}
