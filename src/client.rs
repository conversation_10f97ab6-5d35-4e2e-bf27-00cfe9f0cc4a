

use anyhow::{Context, Result};
use lazy_static::lazy_static;
use reqwest::{Client as HttpClient, ClientBuilder};
use serde_json::json;
use std::{sync::Arc, time::Duration};
use crate::common::JITO_ENDPOINT as JITO_ENDPOINT;
 

lazy_static! {
    pub(crate) static ref HTTP_CLIENT: Arc<Result<HttpClient>> = Arc::new(create_http_client());
}

pub fn create_http_client() -> Result<HttpClient> {
    ClientBuilder::new()
        .timeout(Duration::from_secs(30))
        .pool_idle_timeout(Duration::from_secs(90))
        .pool_max_idle_per_host(10)
        .tcp_keepalive(Duration::from_secs(60))
        .build()
        .context("Failed to build HTTP client")
}

pub fn get_http_client() -> Result<&'static HttpClient> {
    match &**HTTP_CLIENT {
        Ok(client) => Ok(client),
        Err(e) => Err(anyhow::anyhow!("Failed to access HTTP client: {}", e)),
    }
}

pub async fn warmup_jito_connection() -> Result<()> {
    let http_client = get_http_client()?;
    http_client.post(JITO_ENDPOINT)
        .json(&json!({
            "jsonrpc": "2.0",
            "id": "warmup",
            "method": "getHealth",
            "params": []
        }))
        .send()
        .await?;
    Ok(())
}
