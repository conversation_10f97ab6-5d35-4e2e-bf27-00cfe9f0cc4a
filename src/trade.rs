use anyhow::{Context, Result};
use serde_json::{json, Value};
use solana_client::rpc_client::RpcClient;
use solana_program::pubkey::Pubkey;
use solana_sdk::commitment_config::CommitmentConfig;
//use solana_sdk::vote::instruction;
use crate::client::{self, get_http_client};
use crate::common;
use crate::token::get_keypair_from_base58;
use crate::token::{self, get_associated_token_address, get_sell_instruction_no_rpc};
use base64::{engine::general_purpose, Engine as _};
use solana_sdk::{signature::Signer, system_instruction, transaction::Transaction};
use solana_token_seller::instruction::{
    self, AdditionalAccounts, BuyOption, PoolInfo, PoolMintInfo, SellOption, SwapOption,
};
use std::env;
use std::{str::FromStr, time::Instant}; // Import the common module

// Use constants from common module
use crate::common::{JITO_ENDPOINT, JITO_TIP_ACCOUNT};

#[derive(Debug,PartialEq)]
pub enum TradeType {
    Buy,
    Sell,
}

fn get_client() -> RpcClient {
    static RPC_URL: &str = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek";

    RpcClient::new_with_commitment(RPC_URL, CommitmentConfig::processed())
}

pub async fn sell_tokens_jito(client: &RpcClient, mint: &Pubkey, amount: f64) -> Result<String> {
    // Get private key from environment with better error handling
    let private_key =
        env::var("PRIVATE_KEY").context("Missing PRIVATE_KEY environment variable in .env file")?;
    let visible_part = if private_key.len() > 5 {
        &private_key[0..5]
    } else {
        &private_key
    };
    println!(
        "Using private key from .env file starting with: {}...",
        visible_part
    );
    let keypair = get_keypair_from_base58(&private_key)?;

    // Log public key for verification
    println!("Public key: {}", keypair.pubkey());

    // Jito tip amount
    let tip_lamports = 1_000;
    let jito_tip_account = Pubkey::from_str(JITO_TIP_ACCOUNT)?;

    // Build transaction
    let mut instructions = vec![system_instruction::transfer(
        &keypair.pubkey(),
        &jito_tip_account,
        tip_lamports,
    )];

    // Use the global blockhash if available and fresh, otherwise get a new one
    let recent_blockhash = if common::is_blockhash_fresh() {
        if let Some(blockhash) = common::get_blockhash() {
            println!("Using global blockhash: {}", blockhash);
            let hash =
                solana_sdk::hash::Hash::from_str(&blockhash).context("Invalid blockhash format")?;
            hash
        } else {
            println!("No global blockhash available, fetching from RPC");
            client.get_latest_blockhash()?
        }
    } else {
        let age = common::get_blockhash_age_ms().unwrap_or(0);
        println!("Global blockhash too old ({} ms), fetching from RPC", age);
        client.get_latest_blockhash()?
    };

    // Add sell instruction
    let token_amount = (amount * 1.0) as u64;
    println!("Selling {} tokens", token_amount);
    let start_time = Instant::now();
    let sell_instruction = get_sell_instruction_no_rpc(&keypair, mint, token_amount)?;
    let elapsed = start_time.elapsed();
    println!(
        "get_sell_instruction_no_rpc took: {}ms",
        elapsed.as_millis()
    );
    instructions.push(sell_instruction);

    // Create and sign transaction - ensure we're providing the keypair for the signer

    // Debug the transaction setup
    println!(
        "Creating transaction with {} instructions",
        instructions.len()
    );
    println!("Signer: {}", keypair.pubkey());

    let transaction = Transaction::new_signed_with_payer(
        &instructions,
        Some(&keypair.pubkey()),
        &[&keypair], // Make sure the keypair is provided here
        recent_blockhash,
    );

    // Convert transaction to wire format - use the correct serialization method
    let serialized_transaction = bincode::serialize(&transaction)?;

    // Use the recommended Engine::encode method
    let encoded_transaction = general_purpose::STANDARD.encode(&serialized_transaction);

    // Send transaction to Jito
    let elapsed2 = start_time.elapsed();
    println!(" before making request took: {}ms", elapsed2.as_millis());

    let start_time2 = Instant::now();

    // Use the global HTTP client instead of creating a new one
    let http_client = get_http_client()?;
    println!("Reusing existing HTTP client connection for transaction submission");

    let response = http_client
        .post(JITO_ENDPOINT)
        .json(&json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "sendTransaction",
            "params": [encoded_transaction, {"encoding": "base64"}]
        }))
        .send()
        .await?;

    let response_json: Value = response.json().await?;
    let elapsed4 = start_time2.elapsed();
    let elapsed5 = start_time.elapsed();
    println!("  request took: {}ms", elapsed4.as_millis());
    println!("  All took: {}ms", elapsed5.as_millis());
    println!("Response: {}", response_json);

    if let Some(result) = response_json.get("result") {
        if let Some(tx_id) = result.as_str() {
            Ok(tx_id.to_string())
        } else {
            Err(anyhow::anyhow!(
                "Invalid response format from Jito: {:?}",
                result
            ))
        }
    } else if let Some(error) = response_json.get("error") {
        Err(anyhow::anyhow!("Error from Jito: {:?}", error))
    } else {
        Err(anyhow::anyhow!(
            "Unknown response from Jito: {:?}",
            response_json
        ))
    }
}

pub async fn send_tx_jito(
    client: &RpcClient,
    inst: &Vec<solana_sdk::instruction::Instruction>,
) -> Result<String> {
    // Get private key from environment with better error handling
    let private_key =
        env::var("PRIVATE_KEY").context("Missing PRIVATE_KEY environment variable in .env file")?;
    let visible_part = if private_key.len() > 5 {
        &private_key[0..5]
    } else {
        &private_key
    };
   // println!(
   //     "Using private key from .env file starting with: {}...",
   //     visible_part
   // );
    let keypair = get_keypair_from_base58(&private_key)?;

    // Log public key for verification
    //println!("Public key: {}", keypair.pubkey());

    // Jito tip amount
    let tip_lamports = 1_000;
    let jito_tip_account = Pubkey::from_str(JITO_TIP_ACCOUNT)?;

    // Build transaction
    let mut instructions: Vec<solana_sdk::instruction::Instruction> =
        vec![system_instruction::transfer(
            &keypair.pubkey(),
            &jito_tip_account,
            tip_lamports,
        )];

    // Use the global blockhash if available and fresh, otherwise get a new one
    let recent_blockhash = if common::is_blockhash_fresh() {
        if let Some(blockhash) = common::get_blockhash() {
            //println!("Using global blockhash: {}", blockhash);
            let hash =
                solana_sdk::hash::Hash::from_str(&blockhash).context("Invalid blockhash format")?;
            hash
        } else {
            println!("No global blockhash available, fetching from RPC");
            client.get_latest_blockhash()?
        }
    } else {
        let age = common::get_blockhash_age_ms().unwrap_or(0);
        println!("Global blockhash too old ({} ms), fetching from RPC", age);
        client.get_latest_blockhash()?
    };

    // Add sell instruction

    // Add the provided instructions
    instructions.extend_from_slice(inst);

    let start_time = Instant::now();

    // Create and sign transaction - ensure we're providing the keypair for the signer

    // Debug the transaction setup
   // println!(
   //     "Creating transaction with {} instructions",
   //     instructions.len()
  //  );
  //  println!("Signer: {}", keypair.pubkey());

    let transaction = Transaction::new_signed_with_payer(
        &instructions,
        Some(&keypair.pubkey()),
        &[&keypair], // Make sure the keypair is provided here
        recent_blockhash,
    );

    // Convert transaction to wire format - use the correct serialization method
    let serialized_transaction = bincode::serialize(&transaction)?;

    // Use the recommended Engine::encode method
    let encoded_transaction = general_purpose::STANDARD.encode(&serialized_transaction);

    // Send transaction to Jito
    let elapsed2 = start_time.elapsed();
   // println!(" before making request took: {}ms", elapsed2.as_millis());

    let start_time2 = Instant::now();

    // Use the global HTTP client instead of creating a new one
    let http_client = get_http_client()?;
   // println!("Reusing existing HTTP client connection for transaction submission");

    let response = http_client
        .post(JITO_ENDPOINT)
        .json(&json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "sendTransaction",
            "params": [encoded_transaction, {"encoding": "base64"}]
        }))
        .send()
        .await?;

    let response_json: Value = response.json().await?;
    let elapsed4 = start_time2.elapsed();
    let elapsed5 = start_time.elapsed();
   // println!("  request took: {}ms", elapsed4.as_millis());
    //println!("  All took: {}ms", elapsed5.as_millis());
    //println!("Response: {}", response_json);
    // Extract transaction ID from response
    let tx_id = response_json["result"].as_str().unwrap_or_default();
    println!("Transaction ID: \x1b[92mhttps://solscan.io/tx/{}\x1b[0m", tx_id);

    if let Some(result) = response_json.get("result") {
        if let Some(tx_id) = result.as_str() {
            Ok(tx_id.to_string())
        } else {
            Err(anyhow::anyhow!(
                "Invalid response format from Jito: {:?}",
                result
            ))
        }
    } else if let Some(error) = response_json.get("error") {
        Err(anyhow::anyhow!("Error from Jito: {:?}", error))
    } else {
        Err(anyhow::anyhow!(
            "Unknown response from Jito: {:?}",
            response_json
        ))
    }
}

pub async fn swap(
    mut trade: TradeType,
    mint: String,
    pool: String,
    creator: String,
    amount_in: u64,
    amount_out: u64,
    protocol_fee_account: Pubkey,
    is_base_wsol: bool,
) -> Result<()> {
    //println!("Starting swap operation...");
    let private_key =
        env::var("PRIVATE_KEY").context("Missing PRIVATE_KEY environment variable")?;
    let owner = token::get_keypair_from_base58(&private_key)
        .context("Failed to convert PRIVATE_KEY to keypair")?;
    let mut token_amount: u64 = 0;
    let mut sol_amount: u64 = 0;

    trade = if is_base_wsol {
        if trade == TradeType::Buy {
            TradeType::Sell
        } else {
            TradeType::Buy
        }
    } else {
        trade
    };

    if is_base_wsol {
        if trade == TradeType::Buy {
            token_amount = amount_in;
            sol_amount = amount_out;
        } else if trade == TradeType::Sell {
            //this testing now
            sol_amount = amount_in;
            token_amount = amount_out;
        }
    } else {
        if trade == TradeType::Buy {
            sol_amount = amount_in;
            token_amount = amount_out;
        } else if trade == TradeType::Sell {
            token_amount = amount_out;
            sol_amount = amount_in;
        }
    }

    // Create a client that implements Send
    let client = RpcClient::new_with_commitment(
        "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek",
        CommitmentConfig::processed()
    );

    let owner_pubkey = owner.pubkey();
    let mint_pubkey: Pubkey = Pubkey::from_str(&mint).context("Invalid mint address for MINT")?;
    let wsol_pubkey: Pubkey = Pubkey::from_str("So11111111111111111111111111111111111111112")
        .context("Invalid mint address for WSOL")?;
    let creator_pubkey: Pubkey = Pubkey::from_str(&creator).context("Invalid creator address")?;
    let pool_pubkey: Pubkey = Pubkey::from_str(&pool).context("Invalid pool address")?;

    let mint_token_account = get_associated_token_address(&pool_pubkey, &mint_pubkey);
    let wsol_token_account = get_associated_token_address(&pool_pubkey, &wsol_pubkey);

    let pool_base_token_account;
    let pool_quote_token_account;
    let base_mint: Pubkey;
    let quote_mint: Pubkey;

    if is_base_wsol {
        pool_base_token_account = wsol_token_account.clone();
        pool_quote_token_account = mint_token_account.clone();
        base_mint = wsol_pubkey;
        quote_mint = mint_pubkey;
    } else {
        pool_base_token_account = mint_token_account.clone();
        pool_quote_token_account = wsol_token_account.clone();
        base_mint = mint_pubkey;
        quote_mint = wsol_pubkey;
    }

    let pool_info = PoolInfo {
        pool: pool_pubkey,
        pool_base_token_account,
        pool_quote_token_account,
    };
    let pool_mint_info = PoolMintInfo {
        pool: pool_pubkey,
        base_mint: base_mint,
        quote_mint: quote_mint,
    };

    let additional_accounts = AdditionalAccounts {
        coin_creator: creator_pubkey,
    };

    //if trade == TradeType::Buy {
    let option = SwapOption {
        amount_in: amount_in,
        amount_out: amount_out,
    };

    let is_buy = trade == TradeType::Sell;

    let inst: Vec<solana_sdk::instruction::Instruction> = instruction::swap_instruction(
        &pool_info,
        &pool_mint_info,
        &option,
        &mint_pubkey,
        &owner_pubkey,
        additional_accounts,
        protocol_fee_account,
        is_buy,
    );
    send_tx_jito(&client, &inst).await?;



    Ok(())
}

pub async fn burn(mint: String) -> Result<()> {
    //println!("Starting burn operation...");
    let private_key = env::var("PRIVATE_KEY")
        .context("Missing PRIVATE_KEY environment variable")?;
    let owner = token::get_keypair_from_base58(&private_key)
        .context("Failed to convert PRIVATE_KEY to keypair")?;

    // Create RPC client
    let client = RpcClient::new_with_commitment(
        "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek",
        CommitmentConfig::processed()
    );

    let mint_pubkey = Pubkey::from_str(&mint)
        .context("Invalid mint address")?;
    
    // Get token balance
    let token_balance = token::get_token_balance(&client, &mint_pubkey)?;
    //println!("Current token balance: {}", token_balance);
    
    let token_amount = (token_balance * 1000000.0) as u64;
    if token_amount == 0 {
        println!("No tokens to burn");
        return Ok(());
    }

    // Get associated token account
    let token_account = get_associated_token_address(&owner.pubkey(), &mint_pubkey);

    // Create burn instruction
    let burn_instruction = spl_token::instruction::burn(
        &spl_token::id(),
        &token_account,
        &mint_pubkey,
        &owner.pubkey(),
        &[&owner.pubkey()],
        token_amount,
    )?;

    // Create close account instruction
    let close_instruction = spl_token::instruction::close_account(
        &spl_token::id(),
        &token_account,
        &owner.pubkey(),
        &owner.pubkey(),
        &[&owner.pubkey()],
    )?;

    // Combine instructions
    let instructions = vec![burn_instruction, close_instruction];
    
    // Send transaction
    send_tx_jito(&client, &instructions).await?;
    println!("Burn and close operation completed successfully");

    Ok(())
}
