use anyhow::{Context, Result};
use solana_client::rpc_client::RpcClient;
use solana_program::pubkey::Pubkey;
use solana_sdk::{
    commitment_config::CommitmentConfig, instruction::{AccountMeta, Instruction}, signature::{Keypair, Signature, Signer}, signer::SeedDerivable
};
use std::{str::FromStr, time::{Duration, Instant}};
use std::env;
 
const TOKEN_PROGRAM_ID: &str = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";
const ASSOCIATED_TOKEN_PROGRAM_ID: &str = "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL";
const PUMP_FUN_PROGRAM: &str = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
const FEE_RECIPIENT: &str = "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV";
const GLOBAL: &str = "4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf";
const SYSTEM_PROGRAM_ID: &str = "11111111111111111111111111111111";
const EVENT_AUTH: &str = "Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1";
const PUMPSWAP_PROGRAM_ID: &str = "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA";
const PUMPSWAP_EVENT_AUTH: &str = "GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR";
const WSOL_MINT: &str = "So11111111111111111111111111111111111111112"; // Wrapped SOL mint address
const PUMP_SWAP_GLOBAL: &str = "ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw";

pub fn get_associated_token_address(owner: &Pubkey, mint: &Pubkey) -> Pubkey {
    
    let token_program_id = Pubkey::from_str(TOKEN_PROGRAM_ID).unwrap();
    let associated_token_program_id = Pubkey::from_str(ASSOCIATED_TOKEN_PROGRAM_ID).unwrap();
    
    let seeds = &[
        owner.as_ref(),
        token_program_id.as_ref(),
        mint.as_ref(),
    ];
    
    let (address, _) = Pubkey::find_program_address(
        seeds,
        &associated_token_program_id,
    );
    
    address
}

pub fn get_bonding_curve_addresses(mint: &Pubkey) -> Result<(Pubkey, Pubkey)> {
    let pump_fun_program = Pubkey::from_str(PUMP_FUN_PROGRAM)?;
    let associated_token_program_id = Pubkey::from_str(ASSOCIATED_TOKEN_PROGRAM_ID)?;
    let token_program_id = Pubkey::from_str(TOKEN_PROGRAM_ID)?;
    
    // Derive bonding curve address
    let seeds = &[
        b"bonding-curve".as_ref(),
        mint.as_ref(),
    ];
    let (bonding_curve, _) = Pubkey::find_program_address(seeds, &pump_fun_program);
    
    // Derive associated bonding curve address
    let seeds = &[
        bonding_curve.as_ref(),
        token_program_id.as_ref(),
        mint.as_ref(),
    ];
    let (associated_bonding_curve, _) = Pubkey::find_program_address(seeds, &associated_token_program_id);
    
    Ok((bonding_curve, associated_bonding_curve))
}


pub fn get_buy_pumpswap_instruction_no_rpc(keypair: &Keypair, mint: &Pubkey, sol_amount: u64) -> Result<Instruction> {
    let owner = keypair.pubkey();
    
    // Get token account address
    let token_account = get_associated_token_address(&owner, mint);
    
    // Get bonding curve addresses (these serve as pool addresses)
    let (bonding_curve, associated_bonding_curve) = get_bonding_curve_addresses(mint)?;
    
    // Minimum expected token output (set to 0 for demonstration)
    let min_token_output: u64 = 0;

    // Derive coin creator vault authority
    let coin_creator_seeds = &[
        b"creator_vault".as_ref(),
        bonding_curve.as_ref(), // Using bonding_curve as pool creator
    ];
    let pump_fun_program = Pubkey::from_str(PUMP_FUN_PROGRAM)?;
    let (coin_creator_vault_authority, _) = Pubkey::find_program_address(coin_creator_seeds, &pump_fun_program);
    
    // Derive coin creator vault ATA
    let quote_mint = Pubkey::from_str("So11111111111111111111111111111111112")?; // SOL mint
    let coin_creator_vault_ata = get_associated_token_address(&coin_creator_vault_authority, &quote_mint);

    // Create accounts for the buy instruction following the IDL structure
    // 
    let accounts = vec![
        AccountMeta::new_readonly(bonding_curve, false),                      // pool - get from create_pool ???
        AccountMeta::new(owner, true),                                        // user - me  (writable, signer)
        AccountMeta::new_readonly(Pubkey::from_str(PUMP_SWAP_GLOBAL)?, false),          // global_config  ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw
        AccountMeta::new_readonly(*mint, false),                              // base_mint
        AccountMeta::new_readonly(Pubkey::from_str(WSOL_MINT)?, false),                         // quote_mint (SOL) So11111111111111111111111111111111111111112
        AccountMeta::new(token_account, false),                               // user_base_token_account (writable)
        AccountMeta::new(owner, false),                                       // user_quote_token_account (user's SOL account)
        AccountMeta::new(associated_bonding_curve, false),                    // pool_base_token_account (writable)  ??
        AccountMeta::new(bonding_curve, false),                               // pool_quote_token_account (writable)  ??
        AccountMeta::new_readonly(Pubkey::from_str(FEE_RECIPIENT)?, false),   // protocol_fee_recipient //9rPYyANsfQZw3DnDmKE3YCQF5E8oD89UXoHn9JFEhJUz
        AccountMeta::new(get_associated_token_address(&Pubkey::from_str(FEE_RECIPIENT)?, &quote_mint), false), 
        AccountMeta::new_readonly(Pubkey::from_str(TOKEN_PROGRAM_ID)?, false), // base_token_program  TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA
        AccountMeta::new_readonly(Pubkey::from_str(TOKEN_PROGRAM_ID)?, false), // quote_token_program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA
        AccountMeta::new_readonly(Pubkey::from_str(SYSTEM_PROGRAM_ID)?, false), // system_program 11111111111111111111111111111111
        AccountMeta::new_readonly(Pubkey::from_str(ASSOCIATED_TOKEN_PROGRAM_ID)?, false), // associated_token_program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL
        AccountMeta::new_readonly(Pubkey::from_str(PUMPSWAP_EVENT_AUTH)?, false),       // event_authority GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR
        AccountMeta::new_readonly(Pubkey::from_str(PUMPSWAP_PROGRAM_ID)?, false),                   // program pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA
        AccountMeta::new(coin_creator_vault_ata, false),                      // coin_creator_vault_ata (writable) ??
        AccountMeta::new_readonly(coin_creator_vault_authority, false),       // coin_creator_vault_authority ??
    ];

    // Create data for the instruction
    let mut data = vec![];
    
    // Buy instruction discriminator from IDL: [102, 6, 61, 18, 1, 218, 235, 234]
    data.extend_from_slice(&[102, 6, 61, 18, 1, 218, 235, 234]);
    
    // base_amount_out (minimum tokens to receive)
    data.extend_from_slice(&min_token_output.to_le_bytes());
    
    // max_quote_amount_in (maximum SOL to spend)
    data.extend_from_slice(&sol_amount.to_le_bytes());
    
    // Create the instruction
    let buy_instruction = Instruction {
        program_id: pump_fun_program,
        accounts,
        data,
    };
    
    Ok(buy_instruction)
}


pub fn get_sell_instruction_no_rpc(keypair: &Keypair, mint: &Pubkey, token_balance: u64) -> Result<Instruction> {
    let owner = keypair.pubkey();
    
    // Get token account address
    let token_account = get_associated_token_address(&owner, mint);
    
    // Get bonding curve addresses
    let (bonding_curve, associated_bonding_curve) = get_bonding_curve_addresses(mint)?;
    
    // Minimum expected output (set to 0 for demonstration)
    let min_sol_output: u64 = 0;

    // Create accounts for the instruction with correct isSigner flags
    // Only the owner account should be marked as a signer
    let accounts = vec![
        AccountMeta::new_readonly(Pubkey::from_str(GLOBAL)?, false),          // isWritable: false, isSigner: false
        AccountMeta::new(Pubkey::from_str(FEE_RECIPIENT)?, false),            // isWritable: true, isSigner: false
        AccountMeta::new_readonly(*mint, false),                              // isWritable: false, isSigner: false
        AccountMeta::new(bonding_curve, false),                               // isWritable: true, isSigner: false
        AccountMeta::new(associated_bonding_curve, false),                    // isWritable: true, isSigner: false
        AccountMeta::new(token_account, false),                               // isWritable: true, isSigner: false
        AccountMeta::new(owner, true),                                        // isWritable: true, isSigner: true (ONLY THIS ONE IS A SIGNER)
        AccountMeta::new_readonly(Pubkey::from_str(SYSTEM_PROGRAM_ID)?, false), // isWritable: false, isSigner: false
        AccountMeta::new_readonly(Pubkey::from_str(ASSOCIATED_TOKEN_PROGRAM_ID)?, false),  // isWritable: false, isSigner: false
        AccountMeta::new_readonly(Pubkey::from_str(TOKEN_PROGRAM_ID)?, false),  // isWritable: false, isSigner: false
        AccountMeta::new_readonly(Pubkey::from_str(EVENT_AUTH)?, false),        // isWritable: false, isSigner: false
        AccountMeta::new_readonly(Pubkey::from_str(PUMP_FUN_PROGRAM)?, false),  // isWritable: false, isSigner: false
    ];

    // Create data for the instruction
    let mut data = vec![];
    
    // Operation code for sell: "12502976635542562355" 51, 230, 133, 164, 1, 127, 131, 173
    let op_code = 12502976635542562355u64;
    data.extend_from_slice(&op_code.to_le_bytes());
    
    // Token balance to sell
    data.extend_from_slice(&token_balance.to_le_bytes());
    
    // Minimum SOL output
    data.extend_from_slice(&min_sol_output.to_le_bytes());
    
    // Create the instruction
    let sell_instruction = Instruction {
        program_id: Pubkey::from_str(PUMP_FUN_PROGRAM)?,
        accounts,
        data,
    };
    
    // Note: Close token account instruction would be added separately to the transaction
    Ok(sell_instruction)
}

pub fn get_keypair_from_base58(base58_key: &str) -> Result<Keypair> {
    // Check if the key is already without leading/trailing whitespace
    let trimmed_key = base58_key.trim();
    
    // Try to decode the key directly
    match bs58::decode(trimmed_key).into_vec() {
        Ok(decoded) => {
            // Log length for debugging
            //println!("Decoded private key length: {} bytes", decoded.len());
            
            // Check if we got enough bytes for a keypair
            if decoded.len() == 64 {
                // We have a full keypair
                Ok(Keypair::from_bytes(&decoded)?)
            } else if decoded.len() == 32 {
                // This is a secret key only (32 bytes)
                println!("Detected 32-byte secret key, converting to keypair");
                let seed_array: [u8; 32] = decoded.try_into()
                    .map_err(|_| anyhow::anyhow!("Failed to convert seed to 32-byte array"))?;
                
                // Handle the error conversion explicitly
                Keypair::from_seed(&seed_array)
                    .map_err(|e| anyhow::anyhow!("Failed to create keypair from seed: {}", e))
            } else {
                Err(anyhow::anyhow!("Invalid key length: {}, expected 64 bytes for keypair or 32 bytes for secret key", decoded.len()))
            }
        },
        Err(e) => {
            // Try to parse as a JSON array of numbers
            if trimmed_key.starts_with('[') && trimmed_key.ends_with(']') {
                let json_result: Result<Vec<u8>, serde_json::Error> = serde_json::from_str(trimmed_key);
                if let Ok(bytes) = json_result {
                    if bytes.len() == 64 {
                        return Ok(Keypair::from_bytes(&bytes)?);
                    } else if bytes.len() == 32 {
                        let seed_array: [u8; 32] = bytes.try_into()
                            .map_err(|_| anyhow::anyhow!("Failed to convert JSON seed to 32-byte array"))?;
                        
                        // Handle the error conversion explicitly
                        return Keypair::from_seed(&seed_array)
                            .map_err(|e| anyhow::anyhow!("Failed to create keypair from seed: {}", e));
                    }
                }
            }
            
            // If we get here, the key format is not recognized
            Err(anyhow::anyhow!("Failed to decode private key: {}. Make sure it's in base58 format.", e))
        }
    }
}


pub async fn check_transaction_status(client: &RpcClient, tx_id: &str, start_time: Instant) -> Result<bool> {
    let max_attempts = 30; // Maximum number of attempts (30 seconds)
    
    // Convert string tx_id to Signature
    let signature = Signature::from_str(tx_id)
        .context("Invalid transaction signature")?;
    
    for attempt in 1..=max_attempts {
        match client.get_signature_status_with_commitment(
            &signature, 
            CommitmentConfig::processed()
        ) {
            Ok(Some(status)) => {
                if status.is_err() {
                    eprintln!("Transaction failed: {:?}", status);
                    return Ok(false);
                } else {
                    let end_time = Instant::now();
                    let processing_time = end_time.duration_since(start_time).as_millis();
                    println!("Transaction succeeded! Processing time: {}ms", processing_time);
                    return Ok(true);
                }
            },
            Ok(None) => {
                println!("Transaction still processing (attempt {}/{})", attempt, max_attempts);
                
                if attempt >= max_attempts {
                    println!("Max attempts reached. Transaction status uncertain.");
                    return Ok(false);
                }
                
                // Wait 1 second before the next attempt
                tokio::time::sleep(Duration::from_secs(1)).await;
            },
            Err(err) => {
                eprintln!("Error checking transaction: {}", err);
                return Ok(false);
            }
        }
    }
    
    Ok(false)
}

pub fn get_token_balance(client: &RpcClient, mint: &Pubkey) -> Result<f64> {
    // Get private key from environment with better error handling
    let private_key = env::var("PRIVATE_KEY")
        .context("Missing PRIVATE_KEY environment variable. Please check your .env file.")?;
    
    let keypair = match get_keypair_from_base58(&private_key) {
        Ok(kp) => kp,
        Err(err) => {
            eprintln!("Error decoding private key from .env file: {}", err);
            return Err(anyhow::anyhow!("Failed to decode private key from .env file"));
        }
    };
    
    // Find the associated token account for the mint
    let token_account = get_associated_token_address(&keypair.pubkey(), mint);
    
    // Get token account balance
    let balance_result = client.get_token_account_balance(&token_account);
    
    match balance_result {
        Ok(balance) => {
            let amount = balance.ui_amount.unwrap_or(0.0);
            Ok(amount)
        },
        Err(err) => {
            eprintln!("Error getting token balance: {}", err);
            Ok(0.0)
        }
    }
}


pub fn get_sell_pumpswap_instruction_no_rpc(keypair: &Keypair, mint: &Pubkey, token_balance: u64) -> Result<Instruction> {
    let owner = keypair.pubkey();
    
    // Get token account address
    let token_account = get_associated_token_address(&owner, mint);
    
    // Get bonding curve addresses
    let (bonding_curve, associated_bonding_curve) = get_bonding_curve_addresses(mint)?;
    
    // Minimum expected output (set to 0 for demonstration)
    let min_sol_output: u64 = 0;

    // Create accounts for the instruction with correct isSigner flags
    // Only the owner account should be marked as a signer
    let accounts = vec![
        AccountMeta::new_readonly(Pubkey::from_str(GLOBAL)?, false),          // isWritable: false, isSigner: false
        AccountMeta::new(Pubkey::from_str(FEE_RECIPIENT)?, false),            // isWritable: true, isSigner: false
        AccountMeta::new_readonly(*mint, false),                              // isWritable: false, isSigner: false
        AccountMeta::new(bonding_curve, false),                               // isWritable: true, isSigner: false
        AccountMeta::new(associated_bonding_curve, false),                    // isWritable: true, isSigner: false
        AccountMeta::new(token_account, false),                               // isWritable: true, isSigner: false
        AccountMeta::new(owner, true),                                        // isWritable: true, isSigner: true (ONLY THIS ONE IS A SIGNER)
        AccountMeta::new_readonly(Pubkey::from_str(SYSTEM_PROGRAM_ID)?, false), // isWritable: false, isSigner: false
        AccountMeta::new_readonly(Pubkey::from_str(ASSOCIATED_TOKEN_PROGRAM_ID)?, false),  // isWritable: false, isSigner: false
        AccountMeta::new_readonly(Pubkey::from_str(TOKEN_PROGRAM_ID)?, false),  // isWritable: false, isSigner: false
        AccountMeta::new_readonly(Pubkey::from_str(EVENT_AUTH)?, false),        // isWritable: false, isSigner: false
        AccountMeta::new_readonly(Pubkey::from_str(PUMP_FUN_PROGRAM)?, false),  // isWritable: false, isSigner: false
    ];

    // Create data for the instruction
    let mut data = vec![];
    
    // Operation code for sell: "12502976635542562355" 51, 230, 133, 164, 1, 127, 131, 173
    let op_code = 12502976635542562355u64;
    data.extend_from_slice(&op_code.to_le_bytes());
    
    // Token balance to sell
    data.extend_from_slice(&token_balance.to_le_bytes());
    
    // Minimum SOL output
    data.extend_from_slice(&min_sol_output.to_le_bytes());
    
    // Create the instruction
    let sell_instruction = Instruction {
        program_id: Pubkey::from_str(PUMP_FUN_PROGRAM)?,
        accounts,
        data,
    };
    
    // Note: Close token account instruction would be added separately to the transaction
    Ok(sell_instruction)
}