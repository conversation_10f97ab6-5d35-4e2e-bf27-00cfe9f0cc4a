use std::sync::{ Mutex, RwLock};
use lazy_static::lazy_static;

// Constants that might be used across modules
pub const JITO_TIP_ACCOUNT: &str = "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL";
pub const JITO_ENDPOINT: &str = "https://mainnet.block-engine.jito.wtf/api/v1/transactions";
//pub const RPC_URL: &str = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek";

// Global shared state
lazy_static! {
    // Using RwLock for more efficient reads (multiple readers allowed)
    static ref GLOBAL_BLOCKHASH: RwLock<Option<String>> = RwLock::new(None);
    
    // Track when the blockhash was last updated
    static ref BLOCKHASH_TIMESTAMP: Mutex<std::time::Instant> = Mutex::new(std::time::Instant::now());
}

// Function to update the blockhash
pub fn update_blockhash(new_blockhash: String) {
    // Get write access to update the blockhash
    if let Ok(mut blockhash) = GLOBAL_BLOCKHASH.write() {
        *blockhash = Some(new_blockhash);
        
        // Update the timestamp
        if let Ok(mut timestamp) = BLOCKHASH_TIMESTAMP.lock() {
            *timestamp = std::time::Instant::now();
        }
        
        //println!("Global blockhash updated!");
    } else {
        eprintln!("Failed to acquire write lock for blockhash update");
    }
}

// Function to get the current blockhash
pub fn get_blockhash() -> Option<String> {
    // Get read access to retrieve the blockhash
    if let Ok(blockhash) = GLOBAL_BLOCKHASH.read() {
        // Clone the value to avoid holding the lock longer than needed
        blockhash.clone()
    } else {
        eprintln!("Failed to acquire read lock for blockhash");
        None
    }
}

// Function to get the blockhash age in milliseconds
pub fn get_blockhash_age_ms() -> Option<u64> {
    if let Ok(timestamp) = BLOCKHASH_TIMESTAMP.lock() {
        Some(timestamp.elapsed().as_millis() as u64)
    } else {
        None
    }
}

// Check if blockhash is fresh enough (less than 2 seconds old)
pub fn is_blockhash_fresh() -> bool {
    match get_blockhash_age_ms() {
        Some(age) => age < 2000, // Less than 2 seconds
        None => false,
    }
}
